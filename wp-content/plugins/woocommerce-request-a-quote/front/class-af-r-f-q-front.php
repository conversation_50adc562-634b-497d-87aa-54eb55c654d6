<?php
/**
 * Front class start.
 *
 * @package woocommerce-request-a-quote
 */

if ( ! defined( 'WPINC' ) ) {
	die;
}

if ( ! class_exists( 'AF_R_F_Q_Front' ) ) {
	/**
	 * AF_R_F_Q_Front.
	 */
	class AF_R_F_Q_Front extends Addify_Request_For_Quote {
		/**
		 * Construct.
		 */
		public function __construct() {

			add_action( 'wp_enqueue_scripts', array( $this, 'afrfq_front_script' ) );
			add_action( 'wp_loaded', array( $this, 'addify_convert_to_order_customer' ) );
			add_action( 'wp_loaded', array( $this, 'addify_insert_customer_quote' ) );
			add_action( 'wp_nav_menu_items', array( $this, 'afrfq_quote_basket' ), 10, 2 );
			add_shortcode( 'addify-quote-request-page', array( $this, 'addify_quote_request_page_shortcode_function' ) );
			add_shortcode( 'addify-mini-quote', array( $this, 'addify_mini_quote_shortcode_function' ) );
			add_action( 'wp_loaded', array( $this, 'addify_add_endpoints' ) );
			add_filter( 'woocommerce_account_menu_items', array( $this, 'addify_new_menu_items' ) );
			add_action( 'woocommerce_account_request-quote_endpoint', array( $this, 'addify_endpoint_content' ) );
			add_filter( 'query_vars', array( $this, 'addify_add_query_vars' ), 0 );
			add_filter( 'the_title', array( $this, 'addify_endpoint_title' ) );
			add_action( 'wp_loaded', array( $this, 'afrfq_start_customer_session' ) );
			add_action( 'wp_footer', array( $this, 'afrfq_js_pdf_loader' ) );
			add_action( 'wp_head', array( $this, 'afrfq_css_load' ) );
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function afrfq_js_pdf_loader() {
			?>
			<div id="loader-wrapper">
			</div>
			<?php
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function afrfq_load_quote_from_session() {

			if ( isset( wc()->session ) && empty( wc()->session->get( 'quotes' ) ) ) {

				if ( is_user_logged_in() ) {

					$quotes = get_user_meta( get_current_user_id(), 'addify_quote', true );

					if ( ! empty( $quotes ) ) {
						wc()->session->set( 'quotes', $quotes );
					}
				}
			}
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function addify_insert_customer_quote() {

			if ( ! isset( $_POST['afrfq_action'] ) ) {
				return;
			}

			if ( empty( $_REQUEST['afrfq_nonce'] ) || ! wp_verify_nonce( esc_attr( sanitize_text_field( wp_unslash( $_REQUEST['afrfq_nonce'] ) ) ), 'save_afrfq' ) ) {
				die( esc_html__( 'Site security violated.', 'addify_rfq' ) );
			}

			unset( $_POST['afrfq_action'] );

			$data = (array) sanitize_meta( '', wp_unslash( $_POST ), '' );

			// Validate modern form fields
			$validation_result = $this->validate_modern_form_fields( $data );
			if ( ! $validation_result['valid'] ) {
				wc_add_notice( $validation_result['message'], 'error' );
				return;
			}

			// Map modern form fields to legacy field names for compatibility
			$data = $this->map_modern_form_fields( $data );

			$af_quote = new AF_R_F_Q_Quote();

			$af_quote->insert_new_quote( array_merge( $data, (array) $_FILES ) );
		}

		/**
		 * Validate modern form fields with enhanced security and validation.
		 *
		 * @param array $data Form data.
		 * @return array Validation result with 'valid' boolean and 'message' string.
		 */
		private function validate_modern_form_fields( $data ) {
			$errors = array();

			// Required field validation
			$required_fields = array(
				'afrfq_customer_name' => __( 'Name is required.', 'addify_rfq' ),
				'afrfq_company_name'  => __( 'Company Name is required.', 'addify_rfq' ),
				'afrfq_email_address' => __( 'Email Address is required.', 'addify_rfq' ),
				'afrfq_phone_number'  => __( 'Phone Number is required.', 'addify_rfq' ),
				'afrfq_state'         => __( 'State is required.', 'addify_rfq' ),
			);

			foreach ( $required_fields as $field => $error_message ) {
				if ( empty( $data[ $field ] ) || '' === trim( $data[ $field ] ) ) {
					$errors[] = $error_message;
				}
			}

			// Email validation
			if ( ! empty( $data['afrfq_email_address'] ) && ! is_email( $data['afrfq_email_address'] ) ) {
				$errors[] = __( 'Please enter a valid email address.', 'addify_rfq' );
			}

			// Phone number validation (basic)
			if ( ! empty( $data['afrfq_phone_number'] ) ) {
				$phone = preg_replace( '/[^0-9+\-\(\)\s]/', '', $data['afrfq_phone_number'] );
				if ( strlen( $phone ) < 10 ) {
					$errors[] = __( 'Please enter a valid phone number.', 'addify_rfq' );
				}
			}

			// Name validation (no numbers or special characters)
			if ( ! empty( $data['afrfq_customer_name'] ) ) {
				if ( ! preg_match( '/^[a-zA-Z\s\-\'\.]+$/', $data['afrfq_customer_name'] ) ) {
					$errors[] = __( 'Name should only contain letters, spaces, hyphens, apostrophes, and periods.', 'addify_rfq' );
				}
			}

			// Company name validation (allow alphanumeric and common business characters)
			if ( ! empty( $data['afrfq_company_name'] ) ) {
				if ( ! preg_match( '/^[a-zA-Z0-9\s\-\&\.\,\(\)\']+$/', $data['afrfq_company_name'] ) ) {
					$errors[] = __( 'Company name contains invalid characters.', 'addify_rfq' );
				}
			}

			if ( ! empty( $errors ) ) {
				return array(
					'valid'   => false,
					'message' => implode( ' ', $errors ),
				);
			}

			return array(
				'valid'   => true,
				'message' => '',
			);
		}

		/**
		 * Map modern form field names to legacy field names for backward compatibility.
		 *
		 * @param array $data Form data.
		 * @return array Mapped form data.
		 */
		private function map_modern_form_fields( $data ) {
			$field_mapping = array(
				'afrfq_customer_name'      => 'customer_name',
				'afrfq_company_name'       => 'company_name',
				'afrfq_email_address'      => 'customer_email',
				'afrfq_phone_number'       => 'phone_number',
				'afrfq_state'              => 'state',
				'afrfq_company_type'       => 'company_type',
				'afrfq_best_time'          => 'best_time_to_reach',
				'afrfq_division_interest'  => 'division_interest',
			);

			$mapped_data = $data;

			foreach ( $field_mapping as $modern_field => $legacy_field ) {
				if ( isset( $data[ $modern_field ] ) ) {
					// Sanitize the data based on field type
					$sanitized_value = $this->sanitize_field_value( $data[ $modern_field ], $modern_field );
					$mapped_data[ $legacy_field ] = $sanitized_value;
					// Keep both for compatibility
					$mapped_data[ $modern_field ] = $sanitized_value;
				}
			}

			return $mapped_data;
		}

		/**
		 * Sanitize field values based on field type.
		 *
		 * @param mixed  $value Field value.
		 * @param string $field_name Field name.
		 * @return mixed Sanitized value.
		 */
		private function sanitize_field_value( $value, $field_name ) {
			switch ( $field_name ) {
				case 'afrfq_email_address':
					return sanitize_email( $value );

				case 'afrfq_phone_number':
					return sanitize_text_field( $value );

				case 'afrfq_customer_name':
				case 'afrfq_company_name':
				case 'afrfq_state':
				case 'afrfq_best_time':
					return sanitize_text_field( $value );

				case 'afrfq_company_type':
				case 'afrfq_division_interest':
					// Validate against allowed options
					$allowed_company_types = array( 'corporation', 'llc', 'partnership', 'sole_proprietorship', 'nonprofit', 'government', 'other' );
					$allowed_divisions = array( 'sales', 'marketing', 'support', 'technical', 'billing', 'other' );

					if ( 'afrfq_company_type' === $field_name ) {
						return in_array( $value, $allowed_company_types, true ) ? $value : '';
					} elseif ( 'afrfq_division_interest' === $field_name ) {
						return in_array( $value, $allowed_divisions, true ) ? $value : '';
					}
					break;

				default:
					return sanitize_text_field( $value );
			}

			return sanitize_text_field( $value );
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function addify_convert_to_order_customer() {

			if ( ! isset( $_POST['addify_convert_to_order_customer'] ) ) {
				return;
			}

			if ( empty( $_REQUEST['_afrfq__wpnonce'] ) || ! wp_verify_nonce( esc_attr( sanitize_text_field( wp_unslash( $_REQUEST['_afrfq__wpnonce'] ) ) ), '_afrfq__wpnonce' ) ) {
				wp_die( esc_html__( 'Site security violated.', 'addify_rfq' ) );
			}

			$quote_id = sanitize_text_field( wp_unslash( $_POST['addify_convert_to_order_customer'] ) );

			if ( empty( intval( $quote_id ) ) ) {
				return;
			}

			$af_quote = new AF_R_F_Q_Quote();

			$af_quote->convert_quote_to_order( $quote_id );
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function afrfq_start_customer_session() {

			if ( is_user_logged_in() || is_admin() ) {
				return;
			}

			if ( isset( WC()->session ) ) {
				if ( ! WC()->session->has_session() ) {
					WC()->session->set_customer_session_cookie( true );
				}
			}
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function afrfq_front_script() {

			wp_enqueue_style( 'afrfq-front', AFRFQ_URL . 'assets/css/afrfq_front.css', false, AFRFQ_VERSION );
			wp_enqueue_style( 'afrfq-font-awesom', 'https://maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.css', array(), '4.5.0' );
			wp_enqueue_style( 'select2-front', AFRFQ_URL . '/assets/css/select2.css', false, '1.0' );
			wp_enqueue_script( 'jquery' );
			wp_enqueue_script( 'afrfq-frontj', AFRFQ_URL . 'assets/js/afrfq_front.js', array( 'jquery' ), AFRFQ_VERSION, true );
			wp_enqueue_script( 'select2-front', AFRFQ_URL . '/assets/js/select2.js', array( 'jquery' ), '1.0', true );

			// Enqueue modern form enhancement script
			wp_enqueue_script( 'afrfq-modern-form', AFRFQ_URL . 'assets/js/afrfq-modern-form.js', array( 'jquery' ), AFRFQ_VERSION, true );

			$afrfq_data = array(
				'admin_url' => admin_url( 'admin-ajax.php' ),
				'nonce'     => wp_create_nonce( 'afquote-ajax-nonce' ),
				'redirect'  => get_option( 'afrfq_redirect_to_quote' ),
				'pageurl'   => get_page_link( get_option( 'addify_atq_page_id', true ) ),
			);
			wp_localize_script( 'afrfq-frontj', 'afrfq_phpvars', $afrfq_data );

			// Localize modern form script with enhanced data
			$modern_form_data = array(
				'ajaxurl'    => admin_url( 'admin-ajax.php' ),
				'nonce'      => wp_create_nonce( 'afrfq_modern_form_nonce' ),
				'messages'   => array(
					'required'       => __( 'This field is required.', 'addify_rfq' ),
					'invalid_email'  => __( 'Please enter a valid email address.', 'addify_rfq' ),
					'invalid_phone'  => __( 'Please enter a valid phone number.', 'addify_rfq' ),
					'invalid_name'   => __( 'Name should only contain letters, spaces, and common punctuation.', 'addify_rfq' ),
					'submit_error'   => __( 'An error occurred while submitting your request. Please try again.', 'addify_rfq' ),
					'submit_success' => __( 'Your quote request has been submitted successfully!', 'addify_rfq' ),
					'submitting'     => __( 'Submitting...', 'addify_rfq' ),
				),
			);
			wp_localize_script( 'afrfq-modern-form', 'afrfq_modern_form', $modern_form_data );

			wp_enqueue_style( 'dashicons' );

			if ( 'yes' === get_option( 'afrfq_enable_captcha' ) ) {
				wp_enqueue_script( 'Google reCaptcha JS', '//www.google.com/recaptcha/api.js', array( 'jquery' ), '1.0', true );
			}
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function addify_mini_quote_shortcode_function() {

			ob_start();
			wc_get_template(
				'quote/mini-quote.php',
				array(),
				'/woocommerce/addify/rfq/',
				AFRFQ_PLUGIN_DIR . 'templates/'
			);

			return ob_get_clean();
		}

		/**
		 * AF_R_F_Q_Front.
		 *
		 * @param object $items returns items.
		 *
		 * @param object $args returns args.
		 */
		public function afrfq_quote_basket( $items, $args ) {

			if ( is_user_logged_in() ) {
				$user_role = current( wp_get_current_user()->roles );
			} else {
				$user_role = 'guest';
			}

			if ( ! empty( get_option( 'afrfq_customer_roles' ) ) && in_array( $user_role, (array) get_option( 'afrfq_customer_roles' ) ) ) {
				return $items;
			}

			$menu_ids = is_serialized( get_option( 'quote_menu' ) ) ? unserialize( get_option( 'quote_menu' ) ) : get_option( 'quote_menu' );

			if ( empty( $menu_ids ) ) {
				return $items;
			}

			if ( isset( $args->menu->term_id ) ) {

				$menu_id = $args->menu->term_id;

			} elseif ( isset( $args->term_id ) ) {

				$menu_id = $args->term_id;

			} elseif ( isset( $args->menu ) ) {

				$menu_id = $args->menu;

			} else {

				$menu_id = 0;
			}

			$menu_match = in_array( (string) $menu_id, (array) $menu_ids, true ) ? true : false;

			if ( ! $menu_match ) {
				return $items;
			}

			ob_start();

			wc_get_template(
				'quote/mini-quote.php',
				array(),
				'/woocommerce/addify/rfq/',
				AFRFQ_PLUGIN_DIR . 'templates/'
			);

			return $items . ob_get_clean();
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function addify_quote_request_page_shortcode_function() {

			ob_start();

			do_action( 'addify_rfg_success_message' );

			if ( file_exists( get_stylesheet_directory() . '/woocommerce/addify/rfq/front/addify-quote-request-page.php' ) ) {

				require_once get_stylesheet_directory() . '/woocommerce/addify/rfq/front/addify-quote-request-page.php';

			} else {

				wc_get_template(
					'quote/addify-quote-request-page.php',
					array(),
					'/woocommerce/addify/rfq/',
					AFRFQ_PLUGIN_DIR . 'templates/'
				);
			}

			return ob_get_clean();
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function addify_add_endpoints() {

			add_rewrite_endpoint( 'request-quote', EP_ROOT | EP_PAGES );
			flush_rewrite_rules();
		}

		/**
		 * AF_R_F_Q_Front.
		 *
		 * @param object $vars returns vars.
		 */
		public function addify_add_query_vars( $vars ) {
			$vars[] = 'request-quote';
			return $vars;
		}

		/**
		 * AF_R_F_Q_Front.
		 *
		 * @param object $title returns title.
		 */
		public function addify_endpoint_title( $title ) {
			global $wp_query;
			$is_endpoint = isset( $wp_query->query_vars['request-quote'] );
			if ( $is_endpoint && ! is_admin() && is_main_query() && in_the_loop() && is_account_page() ) {
				$title = esc_html__( 'Quotes', 'addify_rfq' );
				remove_filter( 'the_title', array( $this, 'endpoint_title' ) );
			}
			return $title;
		}

		/**
		 * AF_R_F_Q_Front.
		 *
		 * @param object $items returns items.
		 */
		public function addify_new_menu_items( $items ) {
			$logout = $items['customer-logout'];
			unset( $items['customer-logout'] );
			$items['request-quote']   = esc_html__( 'Quotes', 'addify_rfq' );
			$items['customer-logout'] = $logout;
			return $items;
		}

		/**
		 * AF_R_F_Q_Front.
		 */
		public function addify_endpoint_content() {

			$statuses = array(
				'af_pending'    => __( 'Pending', 'addify_rfq' ),
				'af_in_process' => __( 'In Process', 'addify_rfq' ),
				'af_accepted'   => __( 'Accepted', 'addify_rfq' ),
				'af_converted'  => __( 'Converted to Order', 'addify_rfq' ),
				'af_declined'   => __( 'Declined', 'addify_rfq' ),
				'af_cancelled'  => __( 'Cancelled', 'addify_rfq' ),
			);

			$afrfq_id = get_query_var( 'request-quote' );

			$quote = get_post( $afrfq_id );
		
			if ( ! empty( $afrfq_id ) && is_a( $quote, 'WP_Post' ) && 'addify_quote'  == $quote->post_type && ! str_contains($afrfq_id, 'paged=') ) {
				

				$quotedataid = get_post_meta( $afrfq_id, 'quote_proid', true );

				if ( ! empty( $quotedataid ) ) {

					wc_get_template(
						'my-account/quote-details-my-account-old-quotes.php',
						array(
							'afrfq_id' => $afrfq_id,
							'quote'    => $quote,
						),
						'/woocommerce/addify/rfq/',
						AFRFQ_PLUGIN_DIR . 'templates/'
					);

				} else {

					wc_get_template(
						'my-account/quote-details-my-account.php',
						array(
							'afrfq_id' => $afrfq_id,
							'quote'    => $quote,
							'statuses' => $statuses,
						),
						'/woocommerce/addify/rfq/',
						AFRFQ_PLUGIN_DIR . 'templates/'
					);

				}
			} else {

				$afrfq_id = str_replace('paged=', '', $afrfq_id);
				$afrfq_id = str_replace('page/', '', $afrfq_id);

				if ( empty( $afrfq_id ) || 0 == $afrfq_id ) {
					$afrfq_id = 1;
				}
				$quotes_args = array(
					'numberposts' => 5, // Show 5 posts per page
					'meta_key'    => '_customer_user',
					'meta_value'  => get_current_user_id(),
					'post_type'   => 'addify_quote',
					'post_status' => 'publish',
					'paged'       => $afrfq_id,
				);

				// Fetch the posts
				$customer_quotes = get_posts($quotes_args);

				wc_get_template(
					'my-account/quote-list-table.php',
					array(
						'customer_quotes' => $customer_quotes,
						'statuses'        => $statuses,
					),
					'/woocommerce/addify/rfq/',
					AFRFQ_PLUGIN_DIR . 'templates/'
				);

				// Pagination
				$pagination_links = paginate_links(array(
					'format'    => '?paged=%#%', //P's here
					'current'   => max(1, $afrfq_id),
					'total'     => ceil(wp_count_posts('addify_quote')->publish / 5), // Calculate total pages
					'prev_text' => '&laquo; Previous', // Previous button text
					'next_text' => 'Next &raquo;', // Next button text
					'type'      => 'plain', // Output plain pagination without numbers

				));

				// Display pagination links
				if ($pagination_links) {
					$pagination_links = '<div class="af-request-a-quote pagination">' . $pagination_links . '</div>';

					echo wp_kses($pagination_links, wp_kses_allowed_html('post')) ;
				}

			}
		}

		public function afrfq_css_load() {
			$current_theme = wp_get_theme();
			$parent_theme  = $current_theme->parent();
			?>
			<style type="text/css">
			<?php
			if ($current_theme->get('Name') === 'Divi' || ( $parent_theme && $parent_theme->get('Name') === 'Divi' )) {
				?>
				.mini-quote-dropdown{
					box-shadow: 0 2px 5px rgba(0,0,0,.1);
				}
				.addify-rfq-mini-cart__buttons #view-quote{
					color: #fff!important;
					display: block;
					margin-top: 10px!important;
				}
				.mini-quote-dropdown .addify-rfq-mini-cart{
					box-shadow: none!important;
				}
				.woocommerce .addify-quote-form__contents .product-quantity input.qty{
					background-color: #fff!important;
					color: #000!important;
					font-size: 13px!important;
					border: 1px solid #99939333!important;
				}
				.template_two .product-price.offered-price, .template_two .product-subtotal,
				.template_two .product-subtotal.offered-subtotal{
					min-width: auto!important;
				}
				.afrfqbt_single_page + .added_to_quote{
					padding: 12px 20px !important;
					line-height: 23px !important;
					font-size: 17px !important;
					float: left;
				}
			<?php
			}
			if ($current_theme->get('Name') === 'Astra' || ( $parent_theme && $parent_theme->get('Name') === 'Astra' )) {
				?>
					.afrq-menu-item{
							height: 100%;
						display: flex;
						text-decoration: none!important;
						align-items: center;
					}
					.afrfqbt + .added_to_quote{
						display: inline-block!important;
					}
				<?php
			}
			if ($current_theme->get('Name') === 'Avada' || ( $parent_theme && $parent_theme->get('Name') === 'Avada' )) {
				?>
				.addify-quote-form__contents .product-name{
					width: auto!important;
				}
				.addify-quote-form__contents .product-thumbnail{
					float: none!important;
				}
				.addify-quote-form__contents.template-two .product-quantity .quantity{
					width: 89px;
					margin-left: 0;
				}
				.afrfqbt_single_page + .added_to_quote{
					padding: 9px 20px!important;
					line-height: 18px!important;
					font-size: 14px!important;
					display: block!important;
				}
				.afrfqbt + .added_to_quote{
					display: block!important;
					padding-left: 7px ! Important;
				}
				.afrfqbt{
						margin-left: 5px;
				}
				.add_to_cart_button + .afrfqbt + .added_to_quote + .show_details_button{
					float: none !important;
				}
			<?php
			}
			if ($current_theme->get('Name') === 'Woodmart' || ( $parent_theme && $parent_theme->get('Name') === 'Woodmart' )) {
				?>
				form.addify-quote-form.template_one .addify-quote-form__contents .product-quantity .minus,
				form.addify-quote-form.template_one .addify-quote-form__contents .product-quantity .plus{
					background: no-repeat;
					border: 1px solid #99939333;
					line-height: 1.618;
					min-height: auto;
					width: 27px !important;
					padding: 3px 0;
				}
				form.addify-quote-form.template_two .addify-quote-form__contents .product-quantity .minus,
				form.addify-quote-form.template_two .addify-quote-form__contents .product-quantity .plus{
					width: 28px !important;
				}
				.addify-quote-form__contents.template-two th{
					font-size: 13px;
					line-height: 22px;
				}
				.template_two .product-price.offered-price, .template_two .product-subtotal,
				.template_two .product-subtotal.offered-subtotal{
					min-width: 100%!important;
				}
				.afrfqbt + .added_to_quote{
					display: inline-block!important;
					background: no-repeat!important;
					color: inherit!important;
					margin-top: 6px;
					box-shadow: none!important;
				}
				.afrfqbt_single_page + .added_to_quote{
						display: inline-flex;
						align-items: center;
						flex: 0 0 auto!important;
				}
				.afrfqbt.button{
					margin-left: 10px;
					min-height: 36px!important;
				}
			<?php } ?>
			</style>
			<?php
		}
	}
	new AF_R_F_Q_Front();
}
