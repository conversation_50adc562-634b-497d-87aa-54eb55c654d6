<?php
/**
 * Addify Add to Quote Fields
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/rfq/quote/quote-fields.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$user_id = get_current_user_id();

$quote_fields_obj = new AF_R_F_Q_Quote_Fields();
$quote_fields     = (array) $quote_fields_obj->quote_fields;
$cache_data       = wc()->session->get( 'quote_fields_data' );
?>

<div class="quote-fields">

	<?php
	foreach ( $quote_fields as $key => $field ) :

		$field_id = $field->ID;

		$afrfq_field_name        = get_post_meta( $field_id, 'afrfq_field_name', true );
		$afrfq_field_type        = get_post_meta( $field_id, 'afrfq_field_type', true );
		$afrfq_field_label       = get_post_meta( $field_id, 'afrfq_field_label', true );
		$afrfq_field_value       = get_post_meta( $field_id, 'afrfq_field_value', true );
		$afrfq_field_title       = get_post_meta( $field_id, 'afrfq_field_title', true );
		$afrfq_field_placeholder = get_post_meta( $field_id, 'afrfq_field_placeholder', true );
		$afrfq_field_options     = (array) get_post_meta( $field_id, 'afrfq_field_options', true );
		$afrfq_file_types        = get_post_meta( $field_id, 'afrfq_file_types', true );
		$afrfq_file_size         = get_post_meta( $field_id, 'afrfq_file_size', true );
		$afrfq_field_enable      = get_post_meta( $field_id, 'afrfq_field_enable', true );
		$afrfq_field_terms       = get_post_meta( $field_id, 'afrfq_field_terms', true );
		$afrfq_field_width       = get_post_meta( $field_id, 'afrfq_field_width', true );

		// Enhanced field configuration
		$afrfq_field_description = get_post_meta( $field_id, 'afrfq_field_description', true );
		$afrfq_field_css_class   = get_post_meta( $field_id, 'afrfq_field_css_class', true );
		$afrfq_field_min_length  = get_post_meta( $field_id, 'afrfq_field_min_length', true );
		$afrfq_field_max_length  = get_post_meta( $field_id, 'afrfq_field_max_length', true );
		$afrfq_field_min_value   = get_post_meta( $field_id, 'afrfq_field_min_value', true );
		$afrfq_field_max_value   = get_post_meta( $field_id, 'afrfq_field_max_value', true );
		$afrfq_field_pattern     = get_post_meta( $field_id, 'afrfq_field_pattern', true );

		if ( isset( $cache_data[ $afrfq_field_name ] ) ) {
			$field_data = $cache_data[ $afrfq_field_name ];
		} else {
			$field_data = $quote_fields_obj->get_field_default_value( $field_id, $user_id );
		}

		if ( empty( $afrfq_field_name ) ) {
			continue;
		}

		$required = 'yes' === get_post_meta( $field_id, 'afrfq_field_required', true ) ? 'required=required' : '';

		// Build CSS classes
		$css_classes = array( 'addify-option-field' );

		// Add width class
		if ( ! empty( $afrfq_field_width ) ) {
			$css_classes[] = 'adf_' . esc_attr( $afrfq_field_width );
		} else {
			$css_classes[] = 'adf_full_width';
		}

		// Add custom CSS classes
		if ( ! empty( $afrfq_field_css_class ) ) {
			$custom_classes = explode( ' ', $afrfq_field_css_class );
			$css_classes = array_merge( $css_classes, array_map( 'sanitize_html_class', $custom_classes ) );
		}

		$css_class_string = implode( ' ', array_filter( $css_classes ) );

		// Build field attributes
		$field_attributes = array();

		if ( ! empty( $afrfq_field_placeholder ) ) {
			$field_attributes[] = 'placeholder="' . esc_attr( $afrfq_field_placeholder ) . '"';
		}

		if ( ! empty( $afrfq_field_min_length ) && in_array( $afrfq_field_type, array( 'text', 'textarea', 'email', 'url' ), true ) ) {
			$field_attributes[] = 'minlength="' . esc_attr( $afrfq_field_min_length ) . '"';
		}

		if ( ! empty( $afrfq_field_max_length ) && in_array( $afrfq_field_type, array( 'text', 'textarea', 'email', 'url' ), true ) ) {
			$field_attributes[] = 'maxlength="' . esc_attr( $afrfq_field_max_length ) . '"';
		}

		if ( ! empty( $afrfq_field_min_value ) && in_array( $afrfq_field_type, array( 'number', 'range' ), true ) ) {
			$field_attributes[] = 'min="' . esc_attr( $afrfq_field_min_value ) . '"';
		}

		if ( ! empty( $afrfq_field_max_value ) && in_array( $afrfq_field_type, array( 'number', 'range' ), true ) ) {
			$field_attributes[] = 'max="' . esc_attr( $afrfq_field_max_value ) . '"';
		}

		if ( ! empty( $afrfq_field_pattern ) && in_array( $afrfq_field_type, array( 'text', 'email', 'url' ), true ) ) {
			$field_attributes[] = 'pattern="' . esc_attr( $afrfq_field_pattern ) . '"';
		}

		$field_attributes[] = $required;
		$attributes_string = implode( ' ', array_filter( $field_attributes ) );

		if ( 'terms_cond' == $afrfq_field_type ) {
			?>
			<p class="<?php echo esc_attr( $css_class_string ); ?> adf-term-conditon">
				<input type="checkbox" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="yes" <?php echo esc_attr( $required ); ?> >
				<span><?php echo wp_kses_post( $afrfq_field_terms ); ?></span>
				<?php if ( ! empty( $afrfq_field_description ) ) : ?>
					<small class="field-description"><?php echo esc_html( $afrfq_field_description ); ?></small>
				<?php endif; ?>
			</p>
			<?php
			continue;
		}

		?>
		<p class="<?php echo esc_attr( $css_class_string ); ?>">
			<label for="<?php echo esc_attr( $afrfq_field_name ); ?>">
				<?php echo esc_html( $afrfq_field_label ); ?>
				<?php if ( 'yes' === get_post_meta( $field_id, 'afrfq_field_required', true ) ) : ?>
					<span class="required">*</span>
				<?php endif; ?>
			</label>
				<?php
				switch ( $afrfq_field_type ) {
					case 'text':
						?>
						<input type="text" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-text-field">
						<?php
						break;

					case 'phone':
						?>
						<input type="tel" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-phone-field">
						<?php
						break;

					case 'url':
						?>
						<input type="url" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-url-field">
						<?php
						break;

					case 'time':
						?>
						<input type="time" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-time-field">
						<?php
						break;

					case 'date':
						?>
						<input type="date" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-date-field">
						<?php
						break;

					case 'datetime':
						?>
						<input type="datetime-local" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-datetime-field">
						<?php
						break;

					case 'email':
						?>
						<input type="email" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-email-field">
						<?php
						break;

					case 'number':
						?>
						<input type="number" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-number-field">
						<?php
						break;

					case 'range':
						?>
						<input type="range" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-range-field">
						<output for="<?php echo esc_attr( $afrfq_field_name ); ?>" class="range-output"><?php echo esc_html( $field_data ); ?></output>
						<?php
						break;

					case 'color':
						?>
						<input type="color" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-color-field">
						<?php
						break;
					case 'file':
						$accept_types = '';
						if ( ! empty( $afrfq_file_types ) ) {
							$types = array_map( 'trim', explode( ',', $afrfq_file_types ) );
							$accept_types = 'accept=".' . implode( ',.', $types ) . '"';
						}
						?>
						<div class="afrfq-file-upload-wrapper">
							<input type="file" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" <?php echo $accept_types; ?> <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-file-field">
							<label for="<?php echo esc_attr( $afrfq_field_name ); ?>" class="file-upload-label">
								<span class="file-upload-text"><?php echo ! empty( $afrfq_field_placeholder ) ? esc_html( $afrfq_field_placeholder ) : esc_html__( 'Choose file...', 'addify_rfq' ); ?></span>
								<span class="file-upload-button"><?php esc_html_e( 'Browse', 'addify_rfq' ); ?></span>
							</label>
							<?php if ( ! empty( $afrfq_file_types ) ) : ?>
								<small class="file-types-info"><?php printf( esc_html__( 'Allowed file types: %s', 'addify_rfq' ), esc_html( $afrfq_file_types ) ); ?></small>
							<?php endif; ?>
							<?php if ( ! empty( $afrfq_file_size ) ) : ?>
								<small class="file-size-info"><?php printf( esc_html__( 'Maximum file size: %s', 'addify_rfq' ), size_format( $afrfq_file_size ) ); ?></small>
							<?php endif; ?>
						</div>
						<?php
						break;

					case 'textarea':
						$rows = ! empty( $afrfq_field_max_length ) && $afrfq_field_max_length < 500 ? 3 : 5;
						?>
						<textarea id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" rows="<?php echo esc_attr( $rows ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-textarea-field"><?php echo esc_textarea( $field_data ); ?></textarea>
						<?php
						break;
					case 'select':
						?>
						<select id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-select-field">
							<?php if ( ! empty( $afrfq_field_placeholder ) ) : ?>
								<option value=""><?php echo esc_html( $afrfq_field_placeholder ); ?></option>
							<?php endif; ?>
							<?php foreach ( (array) $afrfq_field_options as $option ) :
								$value = strtolower( trim( $option ) );
								?>
								<option value="<?php echo esc_attr( $value ); ?>" <?php echo selected( $value, $field_data ); ?>><?php echo esc_html( $option ); ?></option>
							<?php endforeach; ?>
						</select>
						<?php
						break;

					case 'multiselect':
						?>
						<select id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>[]" multiple <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-multiselect-field">
							<?php foreach ( (array) $afrfq_field_options as $option ) :
								$value = strtolower( trim( $option ) );
								?>
								<option value="<?php echo esc_attr( $value ); ?>" <?php echo in_array( $value, (array) $field_data, true ) ? 'selected' : ''; ?>><?php echo esc_html( $option ); ?></option>
							<?php endforeach; ?>
						</select>
						<small class="multiselect-help"><?php esc_html_e( 'Hold Ctrl (Cmd on Mac) to select multiple options.', 'addify_rfq' ); ?></small>
						<?php
						break;

					case 'radio':
						?>
						<div class="afrfq-radio-group">
							<?php foreach ( (array) $afrfq_field_options as $index => $option ) :
								$value = strtolower( trim( $option ) );
								$radio_id = $afrfq_field_name . '_' . $index;
								?>
								<label class="afrfq-radio-option" for="<?php echo esc_attr( $radio_id ); ?>">
									<input type="radio" id="<?php echo esc_attr( $radio_id ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $value ); ?>" <?php echo checked( $value, $field_data ); ?> <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-radio-field">
									<span class="radio-label"><?php echo esc_html( $option ); ?></span>
								</label>
							<?php endforeach; ?>
						</div>
						<?php
						break;

					case 'checkbox':
						?>
						<div class="afrfq-checkbox-group">
							<?php foreach ( (array) $afrfq_field_options as $index => $option ) :
								$value = strtolower( trim( $option ) );
								$checkbox_id = $afrfq_field_name . '_' . $index;
								?>
								<label class="afrfq-checkbox-option" for="<?php echo esc_attr( $checkbox_id ); ?>">
									<input type="checkbox" id="<?php echo esc_attr( $checkbox_id ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>[]" value="<?php echo esc_attr( $value ); ?>" <?php echo esc_attr( $required ); ?> <?php echo checked( in_array( $value, (array) $field_data ), true ); ?> class="afrfq-field afrfq-checkbox-field">
									<span class="checkbox-label"><?php echo esc_html( $option ); ?></span>
								</label>
							<?php endforeach; ?>
						</div>
						<?php
						break;
				}
				?>

				<?php if ( ! empty( $afrfq_field_description ) ) : ?>
					<small class="field-description"><?php echo esc_html( $afrfq_field_description ); ?></small>
				<?php endif; ?>
			</p>

	<?php endforeach; ?>

</div>

<!-- Enhanced Field Styling -->
<style type="text/css">
/* Enhanced Quote Fields Styling */
.quote-fields {
	max-width: 800px;
	margin: 0 auto;
}

.addify-option-field {
	margin-bottom: 20px;
	position: relative;
}

.addify-option-field label {
	display: block;
	margin-bottom: 8px;
	font-weight: 600;
	color: #2c3e50;
	font-size: 14px;
}

.addify-option-field .required {
	color: #e74c3c;
	margin-left: 3px;
}

/* Field width classes */
.adf_full_width {
	width: 100%;
}

.adf_half_width {
	width: calc(50% - 10px);
	display: inline-block;
	margin-right: 20px;
	vertical-align: top;
}

.adf_third_width {
	width: calc(33.333% - 14px);
	display: inline-block;
	margin-right: 20px;
	vertical-align: top;
}

.adf_quarter_width {
	width: calc(25% - 15px);
	display: inline-block;
	margin-right: 20px;
	vertical-align: top;
}

/* Enhanced field styling */
.afrfq-field {
	width: 100%;
	padding: 12px 16px;
	border: 2px solid #e9ecef;
	border-radius: 6px;
	font-size: 16px;
	line-height: 1.5;
	transition: all 0.3s ease;
	background-color: #ffffff;
	box-sizing: border-box;
}

.afrfq-field:focus {
	outline: none;
	border-color: #f1c40f;
	box-shadow: 0 0 0 3px rgba(241, 196, 15, 0.1);
}

.afrfq-field:invalid {
	border-color: #e74c3c;
}

.afrfq-field.error {
	border-color: #e74c3c;
	background-color: #fdf2f2;
}

/* Specific field type styling */
.afrfq-textarea-field {
	resize: vertical;
	min-height: 100px;
}

.afrfq-range-field {
	padding: 8px 0;
}

.range-output {
	display: inline-block;
	margin-left: 10px;
	padding: 4px 8px;
	background: #f8f9fa;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 600;
	color: #2c3e50;
}

.afrfq-color-field {
	width: 60px;
	height: 40px;
	padding: 4px;
	border-radius: 6px;
	cursor: pointer;
}

/* File upload styling */
.afrfq-file-upload-wrapper {
	position: relative;
}

.afrfq-file-field {
	position: absolute;
	opacity: 0;
	width: 100%;
	height: 100%;
	cursor: pointer;
}

.file-upload-label {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 16px;
	border: 2px dashed #e9ecef;
	border-radius: 6px;
	background: #f8f9fa;
	cursor: pointer;
	transition: all 0.3s ease;
}

.file-upload-label:hover {
	border-color: #f1c40f;
	background: #fffef7;
}

.file-upload-text {
	color: #6c757d;
	font-size: 14px;
}

.file-upload-button {
	background: #f1c40f;
	color: #ffffff;
	padding: 6px 12px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 600;
	text-transform: uppercase;
}

.file-types-info,
.file-size-info {
	display: block;
	margin-top: 5px;
	font-size: 12px;
	color: #6c757d;
}

/* Radio and checkbox groups */
.afrfq-radio-group,
.afrfq-checkbox-group {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.afrfq-radio-option,
.afrfq-checkbox-option {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
	background: #ffffff;
}

.afrfq-radio-option:hover,
.afrfq-checkbox-option:hover {
	border-color: #f1c40f;
	background: #fffef7;
}

.afrfq-radio-option input,
.afrfq-checkbox-option input {
	width: auto;
	margin-right: 10px;
	margin-bottom: 0;
}

.radio-label,
.checkbox-label {
	font-size: 14px;
	color: #2c3e50;
	cursor: pointer;
}

/* Field descriptions */
.field-description {
	display: block;
	margin-top: 5px;
	font-size: 12px;
	color: #6c757d;
	font-style: italic;
	line-height: 1.4;
}

/* Multiselect help text */
.multiselect-help {
	display: block;
	margin-top: 5px;
	font-size: 11px;
	color: #6c757d;
	font-style: italic;
}

/* Terms and conditions styling */
.adf-term-conditon {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 15px;
}

.adf-term-conditon input[type="checkbox"] {
	width: auto;
	margin-right: 10px;
	margin-bottom: 0;
}

.adf-term-conditon span {
	font-size: 14px;
	line-height: 1.6;
	color: #2c3e50;
}

/* Responsive design */
@media (max-width: 768px) {
	.adf_half_width,
	.adf_third_width,
	.adf_quarter_width {
		width: 100%;
		margin-right: 0;
		margin-bottom: 20px;
	}

	.afrfq-radio-group,
	.afrfq-checkbox-group {
		gap: 8px;
	}

	.file-upload-label {
		flex-direction: column;
		gap: 10px;
		text-align: center;
	}
}

/* Enhanced focus and validation states */
.afrfq-field:focus + .field-description {
	color: #f39c12;
}

.afrfq-field.error + .field-description {
	color: #e74c3c;
}

/* Loading and disabled states */
.afrfq-field:disabled {
	background-color: #f8f9fa;
	color: #6c757d;
	cursor: not-allowed;
}

.afrfq-field.loading {
	background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="none" stroke="%23f1c40f" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416"><animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/></circle></svg>');
	background-repeat: no-repeat;
	background-position: right 12px center;
	background-size: 20px 20px;
}
</style>

<!-- Enhanced Field JavaScript -->
<script type="text/javascript">
jQuery(document).ready(function($) {
	// Range field output update
	$('.afrfq-range-field').on('input', function() {
		$(this).next('.range-output').text($(this).val());
	});

	// File upload enhancement
	$('.afrfq-file-field').on('change', function() {
		var fileName = $(this)[0].files[0] ? $(this)[0].files[0].name : '';
		var label = $(this).siblings('.file-upload-label');
		var textSpan = label.find('.file-upload-text');

		if (fileName) {
			textSpan.text(fileName);
			label.addClass('file-selected');
		} else {
			textSpan.text(textSpan.data('original-text') || 'Choose file...');
			label.removeClass('file-selected');
		}
	});

	// Store original file upload text
	$('.file-upload-text').each(function() {
		$(this).data('original-text', $(this).text());
	});

	// Enhanced validation feedback
	$('.afrfq-field').on('blur', function() {
		var field = $(this);
		var isValid = field[0].checkValidity();

		if (isValid) {
			field.removeClass('error');
		} else {
			field.addClass('error');
		}
	});

	// Real-time character count for text fields with maxlength
	$('.afrfq-field[maxlength]').each(function() {
		var field = $(this);
		var maxLength = field.attr('maxlength');
		var counter = $('<small class="char-counter"></small>');
		field.after(counter);

		function updateCounter() {
			var currentLength = field.val().length;
			counter.text(currentLength + '/' + maxLength);

			if (currentLength > maxLength * 0.9) {
				counter.css('color', '#e74c3c');
			} else if (currentLength > maxLength * 0.7) {
				counter.css('color', '#f39c12');
			} else {
				counter.css('color', '#6c757d');
			}
		}

		field.on('input', updateCounter);
		updateCounter();
	});
});
</script>
