<?php
/**
 * Quote Page - Modern Single Column Layout
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/rfq/quote/addify-quote-request-page.php.
 *
 * @package WooCommerce/Templates
 * @version 3.8.0
 */

defined( 'ABSPATH' ) || exit;

$quotes = array();

if ( ! empty( WC()->session ) ) {
	$af_quote = new AF_R_F_Q_Quote( WC()->session->get( 'quotes' ) );
	$quotes = WC()->session->get( 'quotes' );
}

if ( ! empty( $quotes ) ) {
	foreach ( WC()->session->get( 'quotes' ) as $quote_item_key => $quote_item ) {
		if ( isset( $quote_item['quantity'] ) && empty( $quote_item['quantity'] ) ) {
			unset( $quotes[ $quote_item_key ] );
		}
		if ( ! isset( $quote_item['data'] ) ) {
			unset( $quotes[ $quote_item_key ] );
		}
	}
	WC()->session->set( 'quotes', $quotes );
}
?>
<div class="woocommerce afrfq-modern-quote-page">
	<div class="woocommerce-notices-wrapper"><?php wc_print_notices(); ?></div>

<!-- Modern Quote Request Form Styles -->
<style type="text/css">
	.afrfq-modern-quote-page {
		max-width: 1200px;
		margin: 0 auto;
		padding: 20px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
	}

	.afrfq-quote-header {
		text-align: center;
		margin-bottom: 40px;
		padding: 40px 20px;
		background: #f8f9fa;
		border-radius: 8px;
	}

	.afrfq-quote-header h1 {
		color: #2c3e50;
		font-size: 2.5rem;
		font-weight: 600;
		margin-bottom: 20px;
		letter-spacing: -0.5px;
	}

	.afrfq-quote-header p {
		color: #6c757d;
		font-size: 1.1rem;
		line-height: 1.6;
		max-width: 800px;
		margin: 0 auto 20px;
	}

	.afrfq-contact-info {
		background: #e3f2fd;
		padding: 15px 20px;
		border-radius: 6px;
		border-left: 4px solid #2196f3;
		margin-top: 20px;
	}

	.afrfq-modern-form {
		background: #ffffff;
		border-radius: 12px;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
		overflow: hidden;
	}

	.afrfq-quote-products {
		padding: 30px;
		border-bottom: 1px solid #e9ecef;
	}

	.afrfq-quote-form-section {
		padding: 30px;
	}

	.afrfq-section-title {
		font-size: 1.5rem;
		font-weight: 600;
		color: #2c3e50;
		margin-bottom: 25px;
		padding-bottom: 10px;
		border-bottom: 2px solid #f1c40f;
		display: inline-block;
	}
</style>

<?php if ( ! empty( $quotes ) ) :
	$user = null;
	$user_name = '';
	$user_email_add = '';

	if ( is_user_logged_in() ) {
		$user = wp_get_current_user();
		if ( '' == $user->user_firstname && '' == $user->user_lastname ) {
			$user_name = $user->nickname;
		} else {
			$user_name = trim( $user->user_firstname . ' ' . $user->user_lastname );
		}
		$user_email_add = $user->user_email;
	}

	do_action( 'addify_before_quote' );
?>

<!-- Modern Quote Request Header -->
<div class="afrfq-quote-header">
	<h1><?php esc_html_e( 'REQUEST A QUOTE', 'addify_rfq' ); ?></h1>
	<p><?php esc_html_e( 'Our experienced team of Specialists is ready to help you find the best product or service for your needs. Upload your design ideas, current or prior service agreements, evaluation of coverage, or anything else you think will help us bring your vision to life. One of our Specialists will reach out as soon as possible.', 'addify_rfq' ); ?></p>
	<div class="afrfq-contact-info">
		<p><strong><?php esc_html_e( 'Please feel free to reach out to one of our Customer Service Representatives at (800) 470-3570.', 'addify_rfq' ); ?></strong></p>
	</div>
</div>

<!-- Modern Quote Form Container -->
<div class="afrfq-modern-form">
	<!-- Quote Products Section -->
	<div class="afrfq-quote-products">
		<h2 class="afrfq-section-title"><?php esc_html_e( 'Products in Your Quote', 'addify_rfq' ); ?></h2>
		<!-- Modern Quote Products Table -->
		<div class="afrfq-products-table">
			<?php
			if ( file_exists( get_stylesheet_directory() . '/woocommerce/addify/rfq/front/quote-table.php' ) ) {
				include get_stylesheet_directory() . '/woocommerce/addify/rfq/front/quote-table.php';
			} else {
				wc_get_template(
					'quote/quote-table.php',
					array(),
					'/woocommerce/addify/rfq/',
					AFRFQ_PLUGIN_DIR . 'templates/'
				);
			}
			?>
		</div>
	</div>

	<!-- Quote Form Section -->
	<div class="afrfq-quote-form-section">
		<h2 class="afrfq-section-title"><?php esc_html_e( 'Contact Information', 'addify_rfq' ); ?></h2>
		<p class="afrfq-step-indicator"><?php esc_html_e( 'STEP 1 OF 1', 'addify_rfq' ); ?></p>

		<form class="afrfq-modern-contact-form" method="post" enctype="multipart/form-data">
			<!-- Modern Form Fields -->
			<div class="afrfq-form-fields">
				<!-- Name Field (Full Width) -->
				<div class="afrfq-field-group afrfq-full-width">
					<label for="afrfq_customer_name"><?php esc_html_e( '*Name', 'addify_rfq' ); ?></label>
					<input type="text" id="afrfq_customer_name" name="afrfq_customer_name"
						   placeholder="<?php esc_attr_e( 'Name', 'addify_rfq' ); ?>"
						   value="<?php echo esc_attr( $user_name ); ?>" required />
				</div>

				<!-- Company Name and Phone Number (Half Width Each) -->
				<div class="afrfq-field-row">
					<div class="afrfq-field-group afrfq-half-width">
						<label for="afrfq_company_name"><?php esc_html_e( '*Company Name', 'addify_rfq' ); ?></label>
						<input type="text" id="afrfq_company_name" name="afrfq_company_name"
							   placeholder="<?php esc_attr_e( 'Company Name', 'addify_rfq' ); ?>" required />
					</div>
					<div class="afrfq-field-group afrfq-half-width">
						<label for="afrfq_phone_number"><?php esc_html_e( '*Phone Number', 'addify_rfq' ); ?></label>
						<input type="tel" id="afrfq_phone_number" name="afrfq_phone_number"
							   placeholder="<?php esc_attr_e( 'Phone Number', 'addify_rfq' ); ?>" required />
					</div>
				</div>

				<!-- Email Address and State (Half Width Each) -->
				<div class="afrfq-field-row">
					<div class="afrfq-field-group afrfq-half-width">
						<label for="afrfq_email_address"><?php esc_html_e( '*Email Address', 'addify_rfq' ); ?></label>
						<input type="email" id="afrfq_email_address" name="afrfq_email_address"
							   placeholder="<?php esc_attr_e( 'Email Address', 'addify_rfq' ); ?>"
							   value="<?php echo esc_attr( $user_email_add ); ?>" required />
					</div>
					<div class="afrfq-field-group afrfq-half-width">
						<label for="afrfq_state"><?php esc_html_e( '*State', 'addify_rfq' ); ?></label>
						<input type="text" id="afrfq_state" name="afrfq_state"
							   placeholder="<?php esc_attr_e( 'State', 'addify_rfq' ); ?>" required />
					</div>
				</div>

				<!-- Company Type Dropdown -->
				<div class="afrfq-field-group afrfq-full-width">
					<label for="afrfq_company_type"><?php esc_html_e( 'Company Type', 'addify_rfq' ); ?></label>
					<select id="afrfq_company_type" name="afrfq_company_type">
						<option value=""><?php esc_html_e( 'Company Type', 'addify_rfq' ); ?></option>
						<option value="corporation"><?php esc_html_e( 'Corporation', 'addify_rfq' ); ?></option>
						<option value="llc"><?php esc_html_e( 'LLC', 'addify_rfq' ); ?></option>
						<option value="partnership"><?php esc_html_e( 'Partnership', 'addify_rfq' ); ?></option>
						<option value="sole_proprietorship"><?php esc_html_e( 'Sole Proprietorship', 'addify_rfq' ); ?></option>
						<option value="nonprofit"><?php esc_html_e( 'Non-Profit', 'addify_rfq' ); ?></option>
						<option value="government"><?php esc_html_e( 'Government', 'addify_rfq' ); ?></option>
						<option value="other"><?php esc_html_e( 'Other', 'addify_rfq' ); ?></option>
					</select>
				</div>

				<!-- Best Time to Reach You -->
				<div class="afrfq-field-group afrfq-full-width">
					<label for="afrfq_best_time"><?php esc_html_e( 'Best time to reach you?', 'addify_rfq' ); ?></label>
					<input type="text" id="afrfq_best_time" name="afrfq_best_time"
						   placeholder="<?php esc_attr_e( 'Best time to reach you?', 'addify_rfq' ); ?>" />
				</div>

				<!-- Division of Interest Dropdown -->
				<div class="afrfq-field-group afrfq-full-width">
					<label for="afrfq_division_interest"><?php esc_html_e( 'Select which division you\'re interested in:', 'addify_rfq' ); ?></label>
					<select id="afrfq_division_interest" name="afrfq_division_interest">
						<option value=""><?php esc_html_e( 'Select which division you\'re interested in:', 'addify_rfq' ); ?></option>
						<option value="sales"><?php esc_html_e( 'Sales', 'addify_rfq' ); ?></option>
						<option value="marketing"><?php esc_html_e( 'Marketing', 'addify_rfq' ); ?></option>
						<option value="support"><?php esc_html_e( 'Support', 'addify_rfq' ); ?></option>
						<option value="technical"><?php esc_html_e( 'Technical', 'addify_rfq' ); ?></option>
						<option value="billing"><?php esc_html_e( 'Billing', 'addify_rfq' ); ?></option>
						<option value="other"><?php esc_html_e( 'Other', 'addify_rfq' ); ?></option>
					</select>
				</div>
			</div>
			<!-- reCAPTCHA -->
			<?php if ( 'yes' === get_option( 'afrfq_enable_captcha' ) && ! empty( get_option( 'afrfq_site_key' ) ) ) : ?>
				<div class="afrfq-field-group afrfq-full-width">
					<div class="g-recaptcha" data-sitekey="<?php echo esc_attr( get_option( 'afrfq_site_key' ) ); ?>"></div>
				</div>
			<?php endif; ?>

			<!-- Submit Button -->
			<div class="afrfq-submit-section">
				<input name="afrfq_action" type="hidden" value="save_afrfq"/>
				<?php wp_nonce_field( 'save_afrfq', 'afrfq_nonce' ); ?>

				<?php
				$submit_text = get_option( 'afrfq_submit_button_text' );
				$submit_text = empty( $submit_text ) ? __( 'Submit', 'addify_rfq' ) : $submit_text;
				?>

				<button type="submit" name="addify_checkout_place_quote" class="afrfq-submit-btn">
					<?php echo esc_html( $submit_text ); ?>
				</button>

				<!-- Privacy Notice -->
				<div class="afrfq-privacy-notice">
					<p><?php esc_html_e( 'This site is protected by reCAPTCHA and the Google', 'addify_rfq' ); ?>
					<a href="https://policies.google.com/privacy" target="_blank"><?php esc_html_e( 'Privacy Policy', 'addify_rfq' ); ?></a>
					<?php esc_html_e( 'and', 'addify_rfq' ); ?>
					<a href="https://policies.google.com/terms" target="_blank"><?php esc_html_e( 'Terms of Service', 'addify_rfq' ); ?></a>
					<?php esc_html_e( 'apply', 'addify_rfq' ); ?></p>
				</div>
			</div>
		</form>
	</div>

	<!-- Modern Form Styling -->
	<style type="text/css">
		/* Form Field Styling */
		.afrfq-form-fields {
			max-width: 600px;
			margin: 0 auto;
		}

		.afrfq-field-group {
			margin-bottom: 20px;
		}

		.afrfq-field-row {
			display: flex;
			gap: 20px;
			margin-bottom: 0;
		}

		.afrfq-full-width {
			width: 100%;
		}

		.afrfq-half-width {
			flex: 1;
		}

		.afrfq-field-group label {
			display: block;
			margin-bottom: 8px;
			font-weight: 600;
			color: #2c3e50;
			font-size: 14px;
		}

		.afrfq-field-group input,
		.afrfq-field-group select,
		.afrfq-field-group textarea {
			width: 100%;
			padding: 12px 16px;
			border: 2px solid #e9ecef;
			border-radius: 6px;
			font-size: 16px;
			line-height: 1.5;
			transition: border-color 0.3s ease, box-shadow 0.3s ease;
			background-color: #ffffff;
			box-sizing: border-box;
		}

		.afrfq-field-group input:focus,
		.afrfq-field-group select:focus,
		.afrfq-field-group textarea:focus {
			outline: none;
			border-color: #f1c40f;
			box-shadow: 0 0 0 3px rgba(241, 196, 15, 0.1);
		}

		.afrfq-field-group input:invalid {
			border-color: #e74c3c;
		}

		.afrfq-step-indicator {
			color: #6c757d;
			font-size: 14px;
			margin-bottom: 30px;
			font-weight: 500;
		}

		/* Submit Section */
		.afrfq-submit-section {
			text-align: center;
			margin-top: 40px;
			padding-top: 30px;
			border-top: 1px solid #e9ecef;
		}

		.afrfq-submit-btn {
			background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);
			color: #ffffff;
			border: none;
			padding: 16px 40px;
			font-size: 16px;
			font-weight: 600;
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.3s ease;
			text-transform: uppercase;
			letter-spacing: 0.5px;
			box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
		}

		.afrfq-submit-btn:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(241, 196, 15, 0.4);
			background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
		}

		.afrfq-privacy-notice {
			margin-top: 20px;
		}

		.afrfq-privacy-notice p {
			font-size: 12px;
			color: #6c757d;
			line-height: 1.4;
		}

		.afrfq-privacy-notice a {
			color: #2196f3;
			text-decoration: none;
		}

		.afrfq-privacy-notice a:hover {
			text-decoration: underline;
		}

		/* Responsive Design */
		@media (max-width: 768px) {
			.afrfq-modern-quote-page {
				padding: 15px;
			}

			.afrfq-quote-header h1 {
				font-size: 2rem;
			}

			.afrfq-field-row {
				flex-direction: column;
				gap: 0;
			}

			.afrfq-half-width {
				margin-bottom: 20px;
			}

			.afrfq-submit-btn {
				width: 100%;
				padding: 18px;
			}
		}

		/* Products Table Styling */
		.afrfq-products-table table {
			width: 100%;
			border-collapse: collapse;
			margin-bottom: 30px;
			background: #ffffff;
			border-radius: 8px;
			overflow: hidden;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
		}

		.afrfq-products-table th {
			background: #f8f9fa;
			padding: 15px;
			text-align: left;
			font-weight: 600;
			color: #2c3e50;
			border-bottom: 2px solid #e9ecef;
		}

		.afrfq-products-table td {
			padding: 15px;
			border-bottom: 1px solid #f1f3f4;
			vertical-align: middle;
		}

		.afrfq-products-table tr:hover {
			background-color: #f8f9fa;
		}
	</style>

<?php else : ?>
	<!-- Empty Quote State -->
	<div class="afrfq-empty-state">
		<div class="afrfq-empty-content">
			<h2><?php esc_html_e( 'Your quote is currently empty', 'addify_rfq' ); ?></h2>
			<p><?php esc_html_e( 'Add some products to your quote to get started.', 'addify_rfq' ); ?></p>
			<a href="<?php echo esc_url( get_permalink( wc_get_page_id( 'shop' ) ) ); ?>" class="afrfq-shop-btn">
				<?php esc_html_e( 'Continue Shopping', 'addify_rfq' ); ?>
			</a>
		</div>
	</div>

	<style type="text/css">
		.afrfq-empty-state {
			text-align: center;
			padding: 60px 20px;
			background: #f8f9fa;
			border-radius: 12px;
			margin: 40px auto;
			max-width: 600px;
		}

		.afrfq-empty-content h2 {
			color: #2c3e50;
			margin-bottom: 15px;
			font-size: 1.8rem;
		}

		.afrfq-empty-content p {
			color: #6c757d;
			margin-bottom: 30px;
			font-size: 1.1rem;
		}

		.afrfq-shop-btn {
			display: inline-block;
			background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);
			color: #ffffff;
			padding: 14px 30px;
			text-decoration: none;
			border-radius: 6px;
			font-weight: 600;
			transition: all 0.3s ease;
		}

		.afrfq-shop-btn:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
			color: #ffffff;
		}
	</style>
<?php endif; ?>
</div>
