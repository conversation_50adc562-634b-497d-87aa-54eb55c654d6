<?php
/**
 * Field Attributes.
 *
 * Deal field attributes in metabox .
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

global $post;
$field_id = $post->ID;

// Get field types from the enhanced field class
$quote_fields_obj = new AF_R_F_Q_Quote_Fields();
$field_types_config = $quote_fields_obj->get_field_types();

$field_types = array();
foreach ( $field_types_config as $type => $config ) {
	$field_types[ $type ] = $config['label'];
}

$default_values = array(
	''                    => 'Select a default value',
	'user_login'          => __( 'Username', 'addify_rfq' ),
	'first_name'          => __( 'First Name', 'addify_rfq' ),
	'last_name'           => __( 'Last Name', 'addify_rfq' ),
	'nickname'            => __( 'Nickname', 'addify_rfq' ),
	'display_name'        => __( 'Display Name', 'addify_rfq' ),
	'email'               => __( 'Email', 'addify_rfq' ),
	'billing_first_name'  => __( 'Billing First Name', 'addify_rfq' ),
	'billing_last_name'   => __( 'Billing Last Name', 'addify_rfq' ),
	'billing_company'     => __( 'Billing Company', 'addify_rfq' ),
	'billing_address_1'   => __( 'Billing Address 1', 'addify_rfq' ),
	'billing_address_2'   => __( 'Billing Address 2', 'addify_rfq' ),
	'billing_city'        => __( 'Billing City', 'addify_rfq' ),
	'billing_postcode'    => __( 'Billing Postcode', 'addify_rfq' ),
	'billing_phone'       => __( 'Billing Phone', 'addify_rfq' ),
	'billing_email'       => __( 'Billing Email', 'addify_rfq' ),
	'shipping_first_name' => __( 'Shipping First Name', 'addify_rfq' ),
	'shipping_last_name'  => __( 'Shipping Last Name', 'addify_rfq' ),
	'shipping_company'    => __( 'Shipping Company', 'addify_rfq' ),
	'shipping_address_1'  => __( 'Shipping Address 1', 'addify_rfq' ),
	'shipping_address_2'  => __( 'Shipping Address 2', 'addify_rfq' ),
	'shipping_city'       => __( 'Shipping City', 'addify_rfq' ),
	'shipping_postcode'   => __( 'Shipping Postcode', 'addify_rfq' ),
	'shipping_phone'      => __( 'Shipping Phone', 'addify_rfq' ),
	'shipping_email'      => __( 'Shipping Email', 'addify_rfq' ),
);

$afrfq_field_name        = get_post_meta( $field_id, 'afrfq_field_name', true );
$afrfq_field_type        = get_post_meta( $field_id, 'afrfq_field_type', true );
$afrfq_field_label       = get_post_meta( $field_id, 'afrfq_field_label', true );
$afrfq_field_value       = get_post_meta( $field_id, 'afrfq_field_value', true );
$afrfq_field_title       = get_post_meta( $field_id, 'afrfq_field_title', true );
$afrfq_field_placeholder = get_post_meta( $field_id, 'afrfq_field_placeholder', true );
$afrfq_field_options     = (array) get_post_meta( $field_id, 'afrfq_field_options', true );
$afrfq_file_types        = get_post_meta( $field_id, 'afrfq_file_types', true );
$afrfq_file_size         = get_post_meta( $field_id, 'afrfq_file_size', true );
$afrfq_field_enable      = get_post_meta( $field_id, 'afrfq_field_enable', true );
$afrfq_field_terms       = get_post_meta( $field_id, 'afrfq_field_terms', true );
$afrfq_field_width       = get_post_meta( $field_id, 'afrfq_field_width', true );

// Enhanced field configuration options
$afrfq_field_required    = get_post_meta( $field_id, 'afrfq_field_required', true );
$afrfq_field_min_length  = get_post_meta( $field_id, 'afrfq_field_min_length', true );
$afrfq_field_max_length  = get_post_meta( $field_id, 'afrfq_field_max_length', true );
$afrfq_field_min_value   = get_post_meta( $field_id, 'afrfq_field_min_value', true );
$afrfq_field_max_value   = get_post_meta( $field_id, 'afrfq_field_max_value', true );
$afrfq_field_pattern     = get_post_meta( $field_id, 'afrfq_field_pattern', true );
$afrfq_field_description = get_post_meta( $field_id, 'afrfq_field_description', true );
$afrfq_field_css_class   = get_post_meta( $field_id, 'afrfq_field_css_class', true );

if ( empty( $afrfq_field_name ) ) {
	$afrfq_field_name = 'afrfq_field_' . $field_id;
	$readonly         = '';
} else {
	$readonly = 'readonly';
}

?>
<div class="afrfq-metabox-fields">
	<?php wp_nonce_field( 'afrfq_fields_nonce_action', 'afrfq_field_nonce' ); ?>
	<table class="addify-table-optoin">
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Name', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_name" value="<?php echo esc_html( $afrfq_field_name ); ?>" required <?php echo esc_html( $readonly ); ?> >
				<p class="description"><?php echo esc_html__( 'Add a unique name for each quote field. It is also used as meta_key to store values in database. Once publish, you will not be able to modify it.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Type', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select id="afrfq_field_type" name="afrfq_field_type" required>
					<?php foreach ( $field_types as $value => $label ) : ?>
						<option value="<?php echo esc_html( $value ); ?>" <?php echo selected( $value, $afrfq_field_type ); ?> > <?php echo esc_html( $label ); ?> </option>
					<?php endforeach; ?>
				</select>
				<p class="description"></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field label', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_label" value="<?php echo esc_html( $afrfq_field_label ); ?>">
				<p class="description"></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Terms & Conditions', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<textarea rows="5" id="afrfq_field_terms" name="afrfq_field_terms"><?php echo esc_html( $afrfq_field_terms ); ?></textarea>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Default Value', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select name="afrfq_field_value" >
					<?php foreach ( $default_values as $value => $label ) : ?>
						<option value="<?php echo esc_html( $value ); ?>" <?php echo selected( $value, $afrfq_field_value ); ?> > <?php echo esc_html( $label ); ?> </option>
					<?php endforeach; ?>
				</select>
				<p class="description"></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Placeholder', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_placeholder" value="<?php echo esc_html( $afrfq_field_placeholder ); ?>" >
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Width', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select name="afrfq_field_width">
					<option value="full_width" <?php selected( $afrfq_field_width, 'full_width' ); ?>><?php echo esc_html__( 'Full Width', 'addify_rfq' ); ?></option>
					<option value="half_width" <?php selected( $afrfq_field_width, 'half_width' ); ?>><?php echo esc_html__( 'Half Width', 'addify_rfq' ); ?></option>
					<option value="third_width" <?php selected( $afrfq_field_width, 'third_width' ); ?>><?php echo esc_html__( 'One Third Width', 'addify_rfq' ); ?></option>
					<option value="quarter_width" <?php selected( $afrfq_field_width, 'quarter_width' ); ?>><?php echo esc_html__( 'Quarter Width', 'addify_rfq' ); ?></option>
				</select>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Required', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select name="afrfq_field_required">
					<option value="no" <?php selected( $afrfq_field_required, 'no' ); ?>><?php echo esc_html__( 'No', 'addify_rfq' ); ?></option>
					<option value="yes" <?php selected( $afrfq_field_required, 'yes' ); ?>><?php echo esc_html__( 'Yes', 'addify_rfq' ); ?></option>
				</select>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Description', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<textarea name="afrfq_field_description" rows="3" placeholder="<?php esc_attr_e( 'Optional help text for this field', 'addify_rfq' ); ?>"><?php echo esc_textarea( $afrfq_field_description ); ?></textarea>
				<p class="description"><?php echo esc_html__( 'Help text that will be displayed below the field.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'CSS Class', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_css_class" value="<?php echo esc_attr( $afrfq_field_css_class ); ?>" placeholder="<?php esc_attr_e( 'custom-class another-class', 'addify_rfq' ); ?>">
				<p class="description"><?php echo esc_html__( 'Additional CSS classes for styling (space-separated).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Allowed File Types', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_file_types" value="<?php echo esc_html( $afrfq_file_types ); ?>" >
				<p class="description"><?php echo esc_html__( 'Add Comma separated file extensions. Ex. pdf,txt,jpg.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Allowed File Size', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_file_size" value="<?php echo esc_html( $afrfq_file_size ); ?>" >
				<p class="description"><?php echo esc_html__( 'File size in bytes 1KB = 1000 bytes and 1MB = 1000000 bytes', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<!-- Validation Configuration Section -->
		<tr class="addify-option-field validation-field">
			<th colspan="2">
				<div class="option-head">
					<h3 style="border-bottom: 2px solid #f1c40f; padding-bottom: 10px; margin-bottom: 20px;">
						<?php echo esc_html__( 'Validation Settings', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
		</tr>
		<tr class="addify-option-field validation-field text-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Minimum Length', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_min_length" value="<?php echo esc_attr( $afrfq_field_min_length ); ?>" min="0" placeholder="0">
				<p class="description"><?php echo esc_html__( 'Minimum number of characters required.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field text-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Maximum Length', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_max_length" value="<?php echo esc_attr( $afrfq_field_max_length ); ?>" min="0" placeholder="255">
				<p class="description"><?php echo esc_html__( 'Maximum number of characters allowed.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field number-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Minimum Value', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_min_value" value="<?php echo esc_attr( $afrfq_field_min_value ); ?>" step="any" placeholder="0">
				<p class="description"><?php echo esc_html__( 'Minimum numeric value allowed.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field number-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Maximum Value', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_max_value" value="<?php echo esc_attr( $afrfq_field_max_value ); ?>" step="any" placeholder="100">
				<p class="description"><?php echo esc_html__( 'Maximum numeric value allowed.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field pattern-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Validation Pattern', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_pattern" value="<?php echo esc_attr( $afrfq_field_pattern ); ?>" placeholder="^[A-Za-z0-9]+$">
				<p class="description"><?php echo esc_html__( 'Regular expression pattern for custom validation (advanced users only).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field options-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Options', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<?php
				if ( 0 === count( $afrfq_field_options ) ) {
					$afrfq_field_options = array( '' );
				}
				foreach ( $afrfq_field_options as $value ) :
					?>
					<div class="option_row">
						<input type="text" name="afrfq_field_options[]" value="<?php echo esc_html( $value ); ?>" >
						<span type="button" title="Add Option" class="dashicons dashicons-plus-alt2 add_option_button" value="add_more"></span>
						<span type="button" title="Remove Option" class="dashicons dashicons-no-alt remove_option_button" value="add_more"></span>
					</div>
				<?php endforeach; ?>
				<p class="description"><?php echo esc_html__( 'Add Option(s) for fields types ( Select, Multi-Select, Radio, and Checkbox ).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
	</table>
</div>

<!-- Enhanced Field Management JavaScript -->
<script type="text/javascript">
jQuery(document).ready(function($) {
	// Field type configuration
	var fieldTypeConfig = {
		'text': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: ['text-validation', 'pattern-validation']
		},
		'textarea': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: ['text-validation']
		},
		'email': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: []
		},
		'phone': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: []
		},
		'number': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: ['number-validation']
		},
		'url': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: []
		},
		'date': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'time': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'datetime': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'select': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'multiselect': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'radio': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'checkbox': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'file': {
			supports: ['validation', 'width', 'file_types', 'max_size'],
			validations: [],
			showFileOptions: true
		},
		'range': {
			supports: ['default_value', 'validation', 'width'],
			validations: ['number-validation']
		},
		'color': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'terms_cond': {
			supports: ['validation', 'width', 'terms_text'],
			validations: [],
			showTerms: true
		}
	};

	// Function to toggle field visibility based on field type
	function toggleFieldVisibility() {
		var selectedType = $('#afrfq_field_type').val();
		var config = fieldTypeConfig[selectedType] || {};

		// Hide all validation fields first
		$('.validation-field').hide();
		$('.options-field').hide();

		// Show validation section header if any validations are supported
		if (config.validations && config.validations.length > 0) {
			$('.validation-field').first().show(); // Show header

			// Show specific validation fields
			config.validations.forEach(function(validation) {
				$('.' + validation).show();
			});
		}

		// Show options field for select, radio, checkbox, multiselect
		if (config.showOptions) {
			$('.options-field').show();
		}

		// Show file options for file fields
		if (config.showFileOptions) {
			$('tr:has([name="afrfq_file_types"]), tr:has([name="afrfq_file_size"])').show();
		} else {
			$('tr:has([name="afrfq_file_types"]), tr:has([name="afrfq_file_size"])').hide();
		}

		// Show terms field for terms_cond
		if (config.showTerms) {
			$('tr:has([name="afrfq_field_terms"])').show();
		} else {
			$('tr:has([name="afrfq_field_terms"])').hide();
		}

		// Show/hide placeholder field
		if (config.supports && config.supports.includes('placeholder')) {
			$('tr:has([name="afrfq_field_placeholder"])').show();
		} else {
			$('tr:has([name="afrfq_field_placeholder"])').hide();
		}
	}

	// Initialize on page load
	toggleFieldVisibility();

	// Update when field type changes
	$('#afrfq_field_type').on('change', toggleFieldVisibility);

	// Enhanced option management
	$(document).on('click', '.add_option_button', function() {
		var optionRow = $(this).closest('.option_row');
		var newRow = optionRow.clone();
		newRow.find('input').val('');
		optionRow.after(newRow);
	});

	$(document).on('click', '.remove_option_button', function() {
		if ($('.option_row').length > 1) {
			$(this).closest('.option_row').remove();
		}
	});

	// Add field type descriptions
	$('#afrfq_field_type').on('change', function() {
		var selectedType = $(this).val();
		var description = '';

		<?php
		foreach ( $field_types_config as $type => $config ) {
			echo "if (selectedType === '" . esc_js( $type ) . "') { description = '" . esc_js( $config['description'] ) . "'; }\n";
		}
		?>

		$(this).siblings('.description').text(description);
	}).trigger('change');
});
</script>

<!-- Enhanced Field Management Styles -->
<style type="text/css">
.afrfq-metabox-fields .addify-table-optoin {
	width: 100%;
	border-collapse: collapse;
}

.afrfq-metabox-fields .addify-option-field th {
	width: 25%;
	padding: 15px;
	background: #f9f9f9;
	border-bottom: 1px solid #e5e5e5;
	vertical-align: top;
}

.afrfq-metabox-fields .addify-option-field td {
	padding: 15px;
	border-bottom: 1px solid #e5e5e5;
}

.afrfq-metabox-fields .option-head h3 {
	margin: 0;
	font-size: 14px;
	font-weight: 600;
	color: #2c3e50;
}

.afrfq-metabox-fields input[type="text"],
.afrfq-metabox-fields input[type="number"],
.afrfq-metabox-fields select,
.afrfq-metabox-fields textarea {
	width: 100%;
	max-width: 400px;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
}

.afrfq-metabox-fields .description {
	margin-top: 5px;
	font-style: italic;
	color: #666;
	font-size: 12px;
}

.option_row {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	gap: 10px;
}

.option_row input {
	flex: 1;
	max-width: 300px;
}

.add_option_button,
.remove_option_button {
	cursor: pointer;
	color: #0073aa;
	font-size: 16px;
	padding: 5px;
	border-radius: 3px;
	transition: all 0.3s ease;
}

.add_option_button:hover {
	color: #005a87;
	background: #f0f8ff;
}

.remove_option_button:hover {
	color: #d63638;
	background: #fff0f0;
}

.validation-field {
	background: #f8f9fa;
}

.validation-field th,
.validation-field td {
	background: #f8f9fa;
}
</style>
